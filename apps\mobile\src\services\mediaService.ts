import { buildApiUrl, getHeaders } from './api';
import { API_ENDPOINTS } from '../config/api';

export interface MediaUploadResponse {
  uploadUrl: string;
  mediaId: string;
  s3Key: string;
  expiresAt: string;
}

export interface MediaSignedUrlResponse {
  media: {
    id: string;
    original_filename: string;
    mime_type: string;
    file_size: number;
    media_type: string;
    uploaded_by: string;
    uploaded_at: string;
  };
  signedUrl: string;
  expiresAt: string;
}

export interface UploadCompletionData {
  fileSize: number;
  width?: number;
  height?: number;
  duration?: number;
}

export class MediaService {
  private authToken: string;

  constructor(authToken: string) {
    this.authToken = authToken;
  }

  /**
   * Generate a signed URL for direct upload to S3
   */
  async generateUploadUrl(
    filename: string,
    contentType: string,
    expiresIn: number = 3600
  ): Promise<MediaUploadResponse> {
    const response = await fetch(buildApiUrl(API_ENDPOINTS.MEDIA_UPLOAD_URL), {
      method: 'POST',
      headers: getHeaders(this.authToken),
      body: JSON.stringify({
        filename,
        contentType,
        expiresIn,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to generate upload URL: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Upload file directly to S3 using signed URL
   */
  async uploadToS3(uploadUrl: string, file: File | Blob): Promise<void> {
    const response = await fetch(uploadUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file instanceof File ? file.type : 'application/octet-stream',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to upload to S3: ${response.statusText}`);
    }
  }

  /**
   * Complete upload and update media metadata
   */
  async completeUpload(
    mediaId: string,
    completionData: UploadCompletionData
  ): Promise<any> {
    const response = await fetch(buildApiUrl(`${API_ENDPOINTS.MEDIA_COMPLETE_UPLOAD}/${mediaId}`), {
      method: 'PUT',
      headers: getHeaders(this.authToken),
      body: JSON.stringify(completionData),
    });

    if (!response.ok) {
      throw new Error(`Failed to complete upload: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Get signed URL for media access
   */
  async getMediaSignedUrl(
    mediaId: string,
    expiresIn: number = 3600
  ): Promise<MediaSignedUrlResponse> {
    const response = await fetch(
      buildApiUrl(`${API_ENDPOINTS.MEDIA_SIGNED_URL}/${mediaId}?expiresIn=${expiresIn}`),
      {
        method: 'GET',
        headers: getHeaders(this.authToken),
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to get signed URL: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Download media file using signed URL
   */
  async downloadMedia(signedUrl: string): Promise<Blob> {
    const response = await fetch(signedUrl);
    
    if (!response.ok) {
      throw new Error(`Failed to download media: ${response.statusText}`);
    }

    return response.blob();
  }

  /**
   * Complete media upload flow with file processing
   */
  async uploadMediaWithSignedUrl(
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<{ mediaId: string; mediaUrl: string }> {
    try {
      // Step 1: Generate upload URL
      const uploadData = await this.generateUploadUrl(
        file.name,
        file.type,
        3600 // 1 hour
      );

      // Step 2: Upload to S3
      await this.uploadToS3(uploadData.uploadUrl, file);
      if (onProgress) onProgress(50);

      // Step 3: Process file metadata (for images)
      let metadata: UploadCompletionData = {
        fileSize: file.size,
      };

      if (file.type.startsWith('image/')) {
        const imageMetadata = await this.getImageMetadata(file);
        metadata = {
          ...metadata,
          width: imageMetadata.width,
          height: imageMetadata.height,
        };
      }

      // Step 4: Complete upload
      await this.completeUpload(uploadData.mediaId, metadata);
      if (onProgress) onProgress(100);

      return {
        mediaId: uploadData.mediaId,
        mediaUrl: `/api/media/${uploadData.mediaId}/signed-url`,
      };
    } catch (error) {
      console.error('Media upload failed:', error);
      throw error;
    }
  }

  /**
   * Get image metadata (width, height)
   */
  private getImageMetadata(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          width: img.width,
          height: img.height,
        });
      };
      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }
}

// Example usage in a React component:
/*
import { MediaService } from '../services/mediaService';

const MyComponent = () => {
  const [uploadProgress, setUploadProgress] = useState(0);
  const mediaService = new MediaService(authToken);

  const handleFileUpload = async (file: File) => {
    try {
      const result = await mediaService.uploadMediaWithSignedUrl(
        file,
        (progress) => setUploadProgress(progress)
      );
      
      console.log('Upload successful:', result);
      // Use result.mediaId and result.mediaUrl in your app
    } catch (error) {
      console.error('Upload failed:', error);
    }
  };

  const handleMediaDownload = async (mediaId: string) => {
    try {
      const { signedUrl } = await mediaService.getMediaSignedUrl(mediaId);
      const blob = await mediaService.downloadMedia(signedUrl);
      
      // Handle the downloaded blob (save to device, display, etc.)
      const url = URL.createObjectURL(blob);
      // Use the URL for display or download
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  return (
    <div>
      <input 
        type="file" 
        onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0])} 
      />
      {uploadProgress > 0 && <progress value={uploadProgress} max={100} />}
    </div>
  );
};
*/ 