import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
const createSagaMiddleware = require('redux-saga').default
import authReducer from './slices/authSlice';
import chatDBReducer from './slices/chatDBSlice';
import socketReducer from './slices/socketSlice';
import typingReducer from './slices/typingSlice';
import emojiReactionReducer from './slices/emojiReactionSlice';
import fcmReducer from './slices/fcmSlice';
import mediaReducer from './slices/mediaSlice';
import messageSyncReducer from './slices/messageSyncSlice';
import rootSaga from './sagas/rootSaga';
import Reactotron from '../config/ReactotronConfig';

// Persist configuration
const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  whitelist: ['auth', 'fcm'], // Only persist auth and fcm states
  blacklist: ['chatDB', 'socket', 'typing', 'emojiReaction', 'media', 'messageSync'], // Don't persist these states
};

// Create the saga middleware with Reactotron monitoring
const sagaMiddleware = createSagaMiddleware({
  sagaMonitor: __DEV__ ? Reactotron.createSagaMonitor?.() : undefined,
});

// Combine all reducers
const rootReducer = combineReducers({
  auth: authReducer,
  chatDB: chatDBReducer,
  socket: socketReducer,
  typing: typingReducer,
  emojiReaction: emojiReactionReducer,
  fcm: fcmReducer,
  media: mediaReducer,
  messageSync: messageSyncReducer,
});

// Create persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure the store
const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      thunk: true, // Enable thunk for async actions (FCM uses async thunks)
      serializableCheck: {
        // Ignore these action types for redux-persist
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        // Ignore these field paths in all actions
        ignoredActionPaths: ['meta.arg', 'payload.timestamp'],
        // Ignore these paths in the state
        ignoredPaths: ['items.dates'],
      },
    }).concat(sagaMiddleware),
  enhancers: (getDefaultEnhancers) => {
    if (__DEV__ && Reactotron.createEnhancer) {
      return getDefaultEnhancers().concat(Reactotron.createEnhancer());
    }
    return getDefaultEnhancers();
  },
});

// Create persistor
export const persistor = persistStore(store);

// Run the root saga
sagaMiddleware.run(rootSaga);

// Export types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
