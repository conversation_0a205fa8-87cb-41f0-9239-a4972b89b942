import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddS3KeyToMedia1704067200000 implements MigrationInterface {
  name = 'AddS3KeyToMedia1704067200000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'media',
      new TableColumn({
        name: 's3_key',
        type: 'varchar',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('media', 's3_key');
  }
}
