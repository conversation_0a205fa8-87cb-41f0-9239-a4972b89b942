# Mobile App Development Setup

This guide explains how to configure the mobile app to connect to your local backend during development using `.env` files.

## Overview

When developing the mobile app, you need to ensure that API requests from your mobile device can reach your local backend server running on `localhost:3002`. Since mobile devices can't directly access `localhost`, we use your machine's local IP address through environment variables.

## Configuration

### 1. Environment Variables Setup

The app now uses `.env` files for configuration. The main configuration file is:
```
.env
```

### 2. Update Your Local IP Address

To change your local IP address, edit the `.env` file:

```bash
# Open .env file
nano .env
# OR
code .env
```

Update the `LOCAL_IP` variable:
```env
LOCAL_IP=***********
```

### 3. Environment Variables Reference

Here are the available environment variables in `.env`:

```env
# API Configuration
API_URL=http://***********:3002
WS_URL=ws://***********:3002

# Environment
ENV=development
DEBUG=true

# Backend Configuration
BACKEND_PORT=3002
LOCAL_IP=***********

# Logging
LOG_LEVEL=debug
ENABLE_LOGGING=true
```

## How It Works

1. **Development Mode**: When `__DEV__` is true, the app reads from `.env` file:
   - API URL: `http://***********:3002`
   - WebSocket URL: `ws://***********:3002`

2. **Production Mode**: When `__DEV__` is false, the app uses:
   - API URL: `https://tough-disc-merit-median.trycloudflare.com/`
   - WebSocket URL: `wss://tough-disc-merit-median.trycloudflare.com/`

## Testing the Connection

1. Make sure your backend is running on `localhost:3002`
2. Ensure your mobile device and development machine are on the same network
3. Run the mobile app in development mode
4. Check the console logs for any connection errors

## Troubleshooting

### Connection Issues

If the mobile app can't connect to your backend:

1. **Check Network**: Ensure both devices are on the same WiFi network
2. **Verify IP Address**: Double-check your local IP address in `.env` file
3. **Firewall**: Make sure your firewall allows connections on port 3002
4. **Backend CORS**: Ensure your backend allows requests from your mobile app

### IP Address Changes

If your local IP address changes:

1. Run `ipconfig` to get the new IP
2. Update `LOCAL_IP` in `.env` file
3. Restart the development server

### Environment Variable Issues

If environment variables aren't loading:

1. **Check .env file**: Ensure it exists in the mobile app root directory
2. **Restart Metro**: Clear cache and restart Metro bundler
3. **Check Babel config**: Verify `react-native-dotenv` is configured in `babel.config.js`

### Alternative: Using ngrok

If you're having network issues, you can use ngrok to expose your local backend:

```bash
# Install ngrok
npm install -g ngrok

# Expose your backend
ngrok http 3002
```

Then update the `.env` file to use the ngrok URL:
```env
API_URL=https://your-ngrok-url.ngrok.io
WS_URL=wss://your-ngrok-url.ngrok.io
```

## Development Workflow

1. Start your backend server on `localhost:3002`
2. Update the `.env` file with your local IP if needed
3. Run the mobile app in development mode
4. The app will automatically connect to your local backend

## Files Modified

- `.env` - Environment variables (create this file)
- `.env.example` - Example environment variables
- `babel.config.js` - Added react-native-dotenv plugin
- `src/types/env.d.ts` - TypeScript declarations for environment variables
- `src/utils/env.ts` - Updated to use .env variables
- `src/config/development.ts` - Updated to work with .env

## Environment Variable Types

The app includes TypeScript declarations for all environment variables in `src/types/env.d.ts`. This provides:
- Type safety for environment variables
- IntelliSense support in your IDE
- Compile-time checking for missing variables

## Notes

- The configuration automatically switches between development and production based on the `__DEV__` flag
- No need to modify production builds - they will use the cloudflare URLs
- The `.env` file should be added to `.gitignore` to keep sensitive data out of version control
- Use `.env.example` as a template for other developers 