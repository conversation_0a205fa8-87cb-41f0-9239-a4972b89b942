import { call, put, takeLatest, select } from 'redux-saga/effects';
import { PayloadAction } from '@reduxjs/toolkit';
import { apiService, MediaUploadResponse } from '../../services/api';
import { selectAuthToken } from '../slices/authSlice';
import {
  uploadStart,
  uploadProgress,
  uploadSuccess,
  uploadFailure,
  downloadStart,
  downloadProgress,
  downloadSuccess,
  downloadFailure,
} from '../slices/mediaSlice';
import RNFS from 'react-native-fs';
import { chatDBService } from '../../database/service';

// Action types
export const UPLOAD_MEDIA_REQUEST = 'media/uploadRequest';
export const DOWNLOAD_MEDIA_REQUEST = 'media/downloadRequest';

// Action creators
export const uploadMediaRequest = (file: any) => ({
  type: UPLOAD_MEDIA_REQUEST,
  payload: { file }
});

export const downloadMediaRequest = (mediaId: string, messageId: string) => ({
  type: DOWNLOAD_MEDIA_REQUEST,
  payload: { mediaId, messageId }
});

// Media upload saga using direct upload
function* uploadMediaSaga(action: PayloadAction<{
  file: any;
}>): Generator<any, void, any> {
  try {
    const { file } = action.payload;

    // Get auth token from Redux store
    const token: string = yield select(selectAuthToken);
    if (!token) {
      throw new Error('No authentication token available');
    }

    // Set auth token in API service
    apiService.setAuthToken(token);

    console.log('Starting media upload:', file.name || file.uri);

    // Dispatch upload start
    yield put(uploadStart());

    // Upload media using the direct upload method with progress tracking
    const mediaData: MediaUploadResponse = yield call(
      apiService.uploadMedia,
      file,
      function* (progress: number) {
        yield put(uploadProgress(progress));
      }
    );

    console.log('Media upload successful:', mediaData);

    // Dispatch success action
    yield put(uploadSuccess(mediaData));

  } catch (error: any) {
    console.error('Media upload failed:', error);

    const errorMessage = error.response?.data?.message ||
                        error.message ||
                        'Failed to upload media';

    yield put(uploadFailure(errorMessage));
  }
}

// Media download saga
function* downloadMediaSaga(action: PayloadAction<{
  mediaId: string;
  messageId: string;
}>): Generator<any, void, any> {
  try {
    const { mediaId, messageId } = action.payload;

    // Get auth token from Redux store
    const token: string = yield select(selectAuthToken);
    if (!token) {
      throw new Error('No authentication token available');
    }

    // Set auth token in API service
    apiService.setAuthToken(token);

    console.log('Starting media download for mediaId:', mediaId);

    // Dispatch download start
    yield put(downloadStart(mediaId));

    // Get signed URL from backend
    const signedUrlResponse: any = yield call(apiService.getMediaSignedUrl, mediaId);
    const { signedUrl, media } = signedUrlResponse;

    console.log('Got signed URL for download:', signedUrl);

    // Create local file path
    const fileExtension = media.original_filename.split('.').pop() || 'bin';
    const localFileName = `${mediaId}.${fileExtension}`;
    const localFilePath = `${RNFS.DocumentDirectoryPath}/media/${localFileName}`;

    // Ensure media directory exists
    const mediaDir = `${RNFS.DocumentDirectoryPath}/media`;
    const dirExists = yield call(RNFS.exists, mediaDir);
    if (!dirExists) {
      yield call(RNFS.mkdir, mediaDir);
    }

    // Download file with progress tracking
    const downloadOptions = {
      fromUrl: signedUrl,
      toFile: localFilePath,
      progress: (res: any) => {
        const progress = Math.round((res.bytesWritten / res.contentLength) * 100);
        console.log('Download progress:', progress);
        // Progress updates will be handled by the download promise
      },
    };

    const downloadPromise = RNFS.downloadFile(downloadOptions);

    // Track progress with a separate saga or polling mechanism
    const result: any = yield call(() => downloadPromise.promise);

    if (result.statusCode === 200) {
      console.log('Media download successful, saved to:', localFilePath);

      // Update local database with the local file path
      yield call(chatDBService.updateMessageMediaPath, messageId, localFilePath);

      // Dispatch success action
      yield put(downloadSuccess({ mediaId, localPath: localFilePath }));
    } else {
      throw new Error(`Download failed with status: ${result.statusCode}`);
    }

  } catch (error: any) {
    console.error('Media download failed:', error);

    const errorMessage = error.response?.data?.message ||
                        error.message ||
                        'Failed to download media';

    yield put(downloadFailure({ mediaId: action.payload.mediaId, error: errorMessage }));
  }
}

// Media saga watcher
export function* mediaSaga() {
  yield takeLatest(UPLOAD_MEDIA_REQUEST, uploadMediaSaga);
  yield takeLatest(DOWNLOAD_MEDIA_REQUEST, downloadMediaSaga);
}

// Export saga for root saga
export default mediaSaga;