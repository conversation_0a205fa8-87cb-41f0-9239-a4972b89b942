import { all } from 'redux-saga/effects';
import { authSaga } from './authSaga';
import { socketSaga } from './socketSaga';
import { mediaSaga } from './mediaSaga';
import { messageSyncSaga } from './messageSyncSaga';

// Root saga that combines all sagas
export default function* rootSaga() {
  yield all([
    authSaga(),
    socketSaga(),
    mediaSaga(),
    messageSyncSaga(),
    // Add other sagas here as needed
  ]);
}

