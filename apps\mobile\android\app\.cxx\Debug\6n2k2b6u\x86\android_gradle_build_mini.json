{"buildFiles": ["D:\\projecttest\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\projecttest\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-documents\\picker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\projecttest\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\projecttest\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\projecttest\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\projecttest\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\projecttest\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\projecttest\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\projecttest\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\projecttest\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\projecttest\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\projecttest\\chatspot-messenger\\apps\\mobile\\android\\app\\.cxx\\Debug\\6n2k2b6u\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\projecttest\\chatspot-messenger\\apps\\mobile\\android\\app\\.cxx\\Debug\\6n2k2b6u\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"artifactName": "react_codegen_RNVectorIconsSpec", "abi": "x86", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "x86", "output": "D:\\projecttest\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\6n2k2b6u\\obj\\x86\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\6e78c40f366fa67318455816b3ebd5e1\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\d7fd0da1be39f0357ed881cbbe068c29\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\d7fd0da1be39f0357ed881cbbe068c29\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so"]}, "react_codegen_RNPermissionsSpec::@7ad697819b753921c957": {"artifactName": "react_codegen_RNPermissionsSpec", "abi": "x86", "runtimeFiles": []}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"artifactName": "react_codegen_rngesturehandler_codegen", "abi": "x86", "runtimeFiles": []}, "react_codegen_RNImagePickerSpec::@f66ee9a2efecfb28bee4": {"artifactName": "react_codegen_RNImagePickerSpec", "abi": "x86", "runtimeFiles": []}, "react_codegen_rndocumentpickerCGen::@aac1846a245ca418eb66": {"artifactName": "react_codegen_rndocumentpickerCGen", "abi": "x86", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "x86", "output": "D:\\projecttest\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\6n2k2b6u\\obj\\x86\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\d7fd0da1be39f0357ed881cbbe068c29\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\d7fd0da1be39f0357ed881cbbe068c29\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\6e78c40f366fa67318455816b3ebd5e1\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so"]}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "x86", "output": "D:\\projecttest\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\6n2k2b6u\\obj\\x86\\libappmodules.so", "runtimeFiles": ["D:\\projecttest\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\6n2k2b6u\\obj\\x86\\libreact_codegen_safeareacontext.so", "D:\\projecttest\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\6n2k2b6u\\obj\\x86\\libreact_codegen_rnscreens.so", "D:\\projecttest\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\6n2k2b6u\\obj\\x86\\libreact_codegen_rnsvg.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\6e78c40f366fa67318455816b3ebd5e1\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\d7fd0da1be39f0357ed881cbbe068c29\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\d7fd0da1be39f0357ed881cbbe068c29\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so"]}, "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65": {"artifactName": "react_codegen_rnsvg", "abi": "x86", "output": "D:\\projecttest\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\6n2k2b6u\\obj\\x86\\libreact_codegen_rnsvg.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\d7fd0da1be39f0357ed881cbbe068c29\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\d7fd0da1be39f0357ed881cbbe068c29\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\6e78c40f366fa67318455816b3ebd5e1\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so"]}, "react_codegen_RNBootSplashSpec::@05d5bd8b08339ce1ebaa": {"artifactName": "react_codegen_RNBootSplashSpec", "abi": "x86", "runtimeFiles": []}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "x86", "runtimeFiles": []}}}