graph TB
    %% External Users
    User[User]
    Admin[Admin]

    %% Frontend Applications
    subgraph "Frontend Applications"
        WebApp[Web App<br/>React + TypeScript<br/>Vite + Redux]
        MobileApp[Mobile App<br/>React Native<br/>Expo + Redux]
        AdminPanel[Admin Panel<br/>React + Material-UI<br/>Redux Toolkit]
    end

    %% Backend Services
    subgraph "Backend Services (NestJS)"
        AuthModule[Auth Module<br/>JWT + Refresh Tokens<br/>User Management]
        ChatModule[Chat Module<br/>WebSocket Gateway<br/>Message Service]
        AdminModule[Admin Module<br/>User Management<br/>Message Management]
        NotificationsModule[Notifications Module<br/>FCM Integration<br/>Push Notifications]
        AppModule[App Module<br/>Configuration<br/>Database Connection]
    end

    %% Database Layer
    subgraph "Database Layer"
        PostgreSQL[(PostgreSQL<br/>Production Database)]
        SQLite[(SQLite<br/>Development Database)]
        WatermelonDB[(WatermelonDB<br/>Local Mobile Storage)]
    end

    %% External Services
    subgraph "External Services"
        FCM[Firebase Cloud Messaging<br/>Push Notifications]
        MediaStorage[Media Storage<br/>File Upload Service]
        Render[Render<br/>Hosting Platform]
    end

    %% State Management (Internal to Frontend Apps)
    subgraph "Web App State"
        WebReduxStore[Redux Store<br/>Global State Management]
        WebReduxSaga[Redux Saga<br/>Async Operations]
        WebReduxPersist[Redux Persist<br/>State Persistence]
    end

    subgraph "Mobile App State"
        MobileReduxStore[Redux Store<br/>Global State Management]
        MobileReduxSaga[Redux Saga<br/>Async Operations]
        MobileReduxPersist[Redux Persist<br/>State Persistence]
    end

    subgraph "Admin Panel State"
        AdminReduxStore[Redux Store<br/>Global State Management]
        AdminReduxSaga[Redux Saga<br/>Async Operations]
        AdminReduxPersist[Redux Persist<br/>State Persistence]
    end

    %% Local Services
    subgraph "Local Services"
        SocketService[Socket Service<br/>WebSocket Client]
        APIService[API Service<br/>HTTP Client]
        DatabaseService[Database Service<br/>Local Storage]
        NotificationService[Notification Service<br/>Local Notifications]
    end

    %% User Interactions
    User --> WebApp
    User --> MobileApp
    Admin --> AdminPanel

    %% Frontend to Backend
    WebApp --> AuthModule
    WebApp --> ChatModule
    WebApp --> NotificationsModule

    MobileApp --> AuthModule
    MobileApp --> ChatModule
    MobileApp --> NotificationsModule

    AdminPanel --> AuthModule
    AdminPanel --> AdminModule
    AdminPanel --> ChatModule

    %% Backend Internal Dependencies
    AuthModule --> AppModule
    ChatModule --> AppModule
    ChatModule --> AuthModule
    AdminModule --> AppModule
    AdminModule --> AuthModule
    NotificationsModule --> AppModule

    %% Database Connections
    AppModule --> PostgreSQL
    AppModule --> SQLite
    MobileApp --> WatermelonDB
        
    %% External Service Connections
    NotificationsModule --> FCM
    ChatModule --> MediaStorage
    AppModule --> Render

    %% Internal State Management Connections
    WebApp --> WebReduxStore
    WebReduxStore --> WebReduxSaga
    WebReduxStore --> WebReduxPersist

    MobileApp --> MobileReduxStore
    MobileReduxStore --> MobileReduxSaga
    MobileReduxStore --> MobileReduxPersist

    AdminPanel --> AdminReduxStore
    AdminReduxStore --> AdminReduxSaga
    AdminReduxStore --> AdminReduxPersist

    %% Local Service Connections
    WebApp --> SocketService
    WebApp --> APIService
    WebApp --> DatabaseService

    MobileApp --> SocketService
    MobileApp --> APIService
    MobileApp --> DatabaseService
    MobileApp --> NotificationService

    AdminPanel --> APIService

    %% Styling
    classDef userClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef frontendClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef backendClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef databaseClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef externalClass fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef stateClass fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef serviceClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px

    class User,Admin userClass
    class WebApp,MobileApp,AdminPanel frontendClass
    class AuthModule,ChatModule,AdminModule,NotificationsModule,AppModule backendClass
    class PostgreSQL,SQLite,WatermelonDB databaseClass
    class FCM,MediaStorage,Render externalClass
    class WebReduxStore,WebReduxSaga,WebReduxPersist,MobileReduxStore,MobileReduxSaga,MobileReduxPersist,AdminReduxStore,AdminReduxSaga,AdminReduxPersist stateClass
    class SocketService,APIService,DatabaseService,NotificationService serviceClass