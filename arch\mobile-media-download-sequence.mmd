sequenceDiagram
    participant User
    participant ChatWindow
    participant MessageItem
    participant Redux Store
    participant Media Service
    participant Local Storage
    participant File System
    participant API Service
    participant Server
    participant Database

    Note over User,Database: Mobile App Media Download & Save Flow

    Note over User,Database: 1. User Initiates Media Download
    User->>ChatWindow: Tap on media message (image/video/file)
    ChatWindow->>MessageItem: Handle media tap
    MessageItem->>MessageItem: Check if media is already downloaded
    MessageItem->>Local Storage: Check media cache status

    alt Media Already Downloaded
        Local Storage-->>MessageItem: Media found in cache
        MessageItem->>MessageItem: Show media viewer/gallery
        MessageItem->>User: Display media content
    else Media Not Downloaded
        Local Storage-->>MessageItem: Media not in cache
        MessageItem->>MessageItem: Show download button/indicator
        MessageItem->>User: Display download prompt
        
        Note over User,Database: 2. Download Process Initiation
        User->>MessageItem: Tap download button
        MessageItem->>Redux Store: dispatch(downloadMediaRequest)
        Redux Store->>Media Service: downloadMediaSaga triggered
        
        Note over Media Service: 3. Media Download Preparation
        Media Service->>Media Service: Extract media ID from message
        Media Service->>Media Service: Check available storage space
        Media Service->>File System: Get available storage
        File System-->>Media Service: Storage space available
        
        Note over Media Service: 4. Download Progress Tracking
        Media Service->>Redux Store: dispatch(downloadProgressUpdate)
        Redux Store-->>MessageItem: Update download progress UI
        MessageItem->>User: Show download progress bar
        
        Note over Media Service,Server: 5. Media File Download
        Media Service->>API Service: GET /api/media/{mediaId}
        API Service->>Server: Request media file
        Server->>Database: Fetch media metadata
        Database-->>Server: Return media info (URL, type, size)
        Server->>Server: Generate signed download URL
        Server-->>API Service: Return media download URL
        API Service-->>Media Service: Return download URL
        
        Note over Media Service: 6. File Download with Progress
        Media Service->>Media Service: Start file download with progress tracking
        Media Service->>File System: Create temporary file
        File System-->>Media Service: File created
        
        loop Download Progress Updates
            Media Service->>Redux Store: dispatch(downloadProgressUpdate)
            Redux Store-->>MessageItem: Update progress bar
            MessageItem->>User: Show download percentage
        end
        
        Note over Media Service: 7. File Processing & Validation
        Media Service->>Media Service: Validate downloaded file integrity
        Media Service->>Media Service: Check file type and size
        Media Service->>Media Service: Generate file hash for verification
        
        Note over Media Service: 8. Save to Local Storage
        Media Service->>File System: Move file to permanent location
        File System-->>Media Service: File saved successfully
        Media Service->>Local Storage: Save media metadata
        Local Storage-->>Media Service: Metadata saved
        
        Note over Media Service: 9. Update Message Status
        Media Service->>Database: Update message with local file path
        Database-->>Media Service: Message updated
        Media Service->>Redux Store: dispatch(downloadMediaSuccess)
        
        Note over MessageItem: 10. UI Updates
        Redux Store-->>MessageItem: Update message with local file
        MessageItem->>MessageItem: Update media display
        MessageItem->>User: Show downloaded media content
        MessageItem->>User: Display save success notification
    end

    Note over User,Database: 11. Media Viewing & Sharing
    User->>MessageItem: Long press on downloaded media
    MessageItem->>MessageItem: Show media options menu
    MessageItem->>User: Display options (View, Share, Save to Gallery, Delete)
    
    alt User Chooses Save to Gallery
        User->>MessageItem: Tap "Save to Gallery"
        MessageItem->>Media Service: saveToGallery(mediaPath)
        Media Service->>File System: Copy file to gallery directory
        File System-->>Media Service: File copied successfully
        Media Service->>User: Show "Saved to Gallery" notification
    else User Chooses Share
        User->>MessageItem: Tap "Share"
        MessageItem->>Media Service: shareMedia(mediaPath)
        Media Service->>User: Open native share dialog
    else User Chooses Delete
        User->>MessageItem: Tap "Delete"
        MessageItem->>Media Service: deleteLocalMedia(mediaPath)
        Media Service->>File System: Delete local file
        File System-->>Media Service: File deleted
        Media Service->>Local Storage: Remove media metadata
        Local Storage-->>Media Service: Metadata removed
        Media Service->>Redux Store: dispatch(mediaDeleted)
        Redux Store-->>MessageItem: Update message status
        MessageItem->>User: Show "Media deleted" notification
    end

    Note over User,Database: 12. Error Handling
    Note over Media Service: Network Error
    Media Service->>Redux Store: dispatch(downloadMediaError)
    Redux Store-->>MessageItem: Show error state
    MessageItem->>User: Display retry button
    
    Note over Media Service: Storage Full Error
    Media Service->>Redux Store: dispatch(storageFullError)
    Redux Store-->>MessageItem: Show storage error
    MessageItem->>User: Display storage management prompt
    
    Note over Media Service: File Corruption Error
    Media Service->>Media Service: Delete corrupted file
    Media Service->>Redux Store: dispatch(fileCorruptionError)
    Redux Store-->>MessageItem: Show corruption error
    MessageItem->>User: Display retry download option

    Note over User,Database: 13. Background Download Management
    Note over Media Service: Auto-cleanup old media files
    Media Service->>Local Storage: Check media cache size
    Local Storage-->>Media Service: Cache size exceeds limit
    Media Service->>File System: Delete oldest media files
    File System-->>Media Service: Cleanup completed
    Media Service->>Local Storage: Update cache metadata 