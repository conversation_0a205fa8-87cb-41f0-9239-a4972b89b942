# S3 Direct Upload Setup Guide

This guide explains how to configure your ChatSpot backend to upload files directly to Amazon S3 instead of storing them locally.

## Prerequisites

1. AWS Account with S3 access
2. S3 bucket created
3. IAM user with appropriate S3 permissions

## AWS Setup

### 1. Create S3 Bucket

1. Go to AWS S3 Console
2. Click "Create bucket"
3. Choose a unique bucket name (e.g., `chatspot-media-uploads`)
4. Select your preferred region
5. Configure bucket settings as needed
6. Create the bucket

### 2. Create IAM User

1. Go to AWS IAM Console
2. Click "Users" → "Add user"
3. Choose a username (e.g., `chatspot   `)
4. Select "Programmatic access"
5. Attach the following policy (or create a custom policy):

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject"
            ],
            "Resource": "arn:aws:s3:::your-bucket-name/*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "s3:ListBucket"
            ],
            "Resource": "arn:aws:s3:::your-bucket-name"
        }
    ]
}
```

6. Save the Access Key ID and Secret Access Key

### 3. Configure CORS (Optional)

If you plan to upload directly from the frontend, configure CORS on your S3 bucket:

```json
[
    {
        "AllowedHeaders": ["*"],
        "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
        "AllowedOrigins": ["*"],
        "ExposeHeaders": []
    }
]
```

## Backend Configuration

### 1. Environment Variables

Add the following variables to your `.env` file:

```env
# S3 Storage Configuration
USE_S3_STORAGE=true
AWS_ACCESS_KEY_ID=your_access_key_id
AWS_SECRET_ACCESS_KEY=your_secret_access_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-bucket-name
```

### 2. Run Database Migration

Run the migration to add the S3 key field to the media table:

```bash
yarn run migration:run
```

## Features

### Current Implementation

- ✅ Direct upload to S3 using multer-s3
- ✅ Automatic file naming with timestamps
- ✅ File size validation (10MB limit)
- ✅ MIME type validation (images only)
- ✅ Image metadata extraction (width, height)
- ✅ Fallback to local storage when S3 is disabled
- ✅ File retrieval from S3
- ✅ File deletion from S3

### Limitations

- ❌ Thumbnail generation for S3 uploads (can be added later)
- ❌ Video/audio support (images only currently)
- ❌ Presigned URL generation for direct frontend uploads

## Usage

### Upload Endpoint

```
POST /api/media/upload
Content-Type: multipart/form-data

Body:
- file: [binary file data]
```

### Response

```json
{
  "id": "uuid",
  "original_filename": "image.jpg",
  "filename": "media/1704067200000-abc123.jpg",
  "mime_type": "image/jpeg",
  "file_size": 1024000,
  "media_type": "image",
  "file_path": "https://bucket.s3.region.amazonaws.com/media/1704067200000-abc123.jpg",
  "uploaded_by": "username",
  "width": 1920,
  "height": 1080,
  "s3_key": "media/1704067200000-abc123.jpg",
  "uploaded_at": "2023-12-31T12:00:00.000Z"
}
```

## Switching Between Local and S3 Storage

You can switch between local and S3 storage by changing the `USE_S3_STORAGE` environment variable:

- `USE_S3_STORAGE=false` - Files stored locally in `uploads/` directory
- `USE_S3_STORAGE=true` - Files uploaded directly to S3

## Cost Considerations

- S3 storage costs vary by region and usage
- PUT requests (uploads) are charged per request
- GET requests (downloads) are charged per request
- Data transfer costs may apply
- Consider using S3 lifecycle policies for cost optimization

## Security Best Practices

1. Use IAM roles with minimal required permissions
2. Enable S3 bucket versioning for data protection
3. Consider enabling S3 server-side encryption
4. Regularly rotate access keys
5. Monitor S3 access logs
6. Use presigned URLs for temporary access when needed

## Troubleshooting

### Common Issues

1. **Access Denied**: Check IAM permissions and bucket policy
2. **Region Mismatch**: Ensure AWS_REGION matches your bucket region
3. **CORS Errors**: Configure CORS policy on S3 bucket
4. **File Not Found**: Verify S3 key is correctly stored in database

### Debug Mode

Enable debug logging by setting `NODE_ENV=development` to see detailed S3 operation logs.
