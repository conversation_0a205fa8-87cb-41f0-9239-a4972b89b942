import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { MediaUploadResponse } from '../../services/api';

interface MediaState {
  uploading: boolean;
  uploadProgress: number;
  uploadedMedia: MediaUploadResponse | null;
  error: string | null;
  downloading: { [mediaId: string]: boolean };
  downloadProgress: { [mediaId: string]: number };
  downloadedMedia: { [mediaId: string]: string }; // mediaId -> local file path
}

const initialState: MediaState = {
  uploading: false,
  uploadProgress: 0,
  uploadedMedia: null,
  error: null,
  downloading: {},
  downloadProgress: {},
  downloadedMedia: {},
};

const mediaSlice = createSlice({
  name: 'media',
  initialState,
  reducers: {
    uploadStart: (state) => {
      state.uploading = true;
      state.uploadProgress = 0;
      state.error = null;
      state.uploadedMedia = null;
    },
    uploadProgress: (state, action: PayloadAction<number>) => {
      state.uploadProgress = action.payload;
    },
    uploadSuccess: (state, action: PayloadAction<MediaUploadResponse>) => {
      state.uploading = false;
      state.uploadProgress = 100;
      state.uploadedMedia = action.payload;
      state.error = null;
    },
    uploadFailure: (state, action: PayloadAction<string>) => {
      state.uploading = false;
      state.uploadProgress = 0;
      state.error = action.payload;
      state.uploadedMedia = null;
    },
    clearMediaState: (state) => {
      state.uploading = false;
      state.uploadProgress = 0;
      state.uploadedMedia = null;
      state.error = null;
    },
    // Download actions
    downloadStart: (state, action: PayloadAction<string>) => {
      const mediaId = action.payload;
      state.downloading[mediaId] = true;
      state.downloadProgress[mediaId] = 0;
      state.error = null;
    },
    downloadProgress: (state, action: PayloadAction<{ mediaId: string; progress: number }>) => {
      const { mediaId, progress } = action.payload;
      state.downloadProgress[mediaId] = progress;
    },
    downloadSuccess: (state, action: PayloadAction<{ mediaId: string; localPath: string }>) => {
      const { mediaId, localPath } = action.payload;
      state.downloading[mediaId] = false;
      state.downloadProgress[mediaId] = 100;
      state.downloadedMedia[mediaId] = localPath;
      state.error = null;
    },
    downloadFailure: (state, action: PayloadAction<{ mediaId: string; error: string }>) => {
      const { mediaId, error } = action.payload;
      state.downloading[mediaId] = false;
      state.downloadProgress[mediaId] = 0;
      state.error = error;
    },
  },
});

export const {
  uploadStart,
  uploadProgress,
  uploadSuccess,
  uploadFailure,
  clearMediaState,
  downloadStart,
  downloadProgress,
  downloadSuccess,
  downloadFailure,
} = mediaSlice.actions;

// Selectors
export const selectMediaUploading = (state: { media: MediaState }) => state.media.uploading;
export const selectMediaUploadProgress = (state: { media: MediaState }) => state.media.uploadProgress;
export const selectUploadedMedia = (state: { media: MediaState }) => state.media.uploadedMedia;
export const selectMediaError = (state: { media: MediaState }) => state.media.error;
export const selectMediaDownloading = (state: { media: MediaState }, mediaId: string) =>
  state.media.downloading[mediaId] || false;
export const selectMediaDownloadProgress = (state: { media: MediaState }, mediaId: string) =>
  state.media.downloadProgress[mediaId] || 0;
export const selectDownloadedMediaPath = (state: { media: MediaState }, mediaId: string) =>
  state.media.downloadedMedia[mediaId];

export default mediaSlice.reducer;