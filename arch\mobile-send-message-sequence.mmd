sequenceDiagram
    participant User
    participant MessageInput
    participant Redux Store
    participant Socket Saga
    participant Local DB
    participant WebSocket
    participant Server
    participant Receiver

    Note over User,Receiver: Mobile App Send Message Flow

    User->>MessageInput: Type message & press send
    MessageInput->>MessageInput: Validate message & connection
    MessageInput->>Redux Store: dispatch(sendMessageRequest)
    Redux Store->>Socket Saga: sendMessage<PERSON>aga triggered

    Note over Socket Saga: Message Preparation
    Socket Saga->>Socket Saga: Get current user from auth state
    Socket Saga->>Local DB: saveMessage(sender, receiver, text, true, 'text', 'sending')
    Local DB-->>Socket Saga: Return saved message with ID
    Socket Saga->>Socket Saga: Create messageData with client_message_id

    Note over Socket Saga,WebSocket: Send via WebSocket
    Socket Saga->>WebSocket: socket.emit('message', messageData, ackCallback)
    WebSocket->>Server: Forward message to server
    Server->>Server: Process message & generate server ID
    Server-->>WebSocket: Acknowledgment with status & server ID
    WebSocket-->>Socket Saga: ackCallback(response)

    Note over Socket Saga: Update Local Status
    Socket Saga->>Local DB: updateMessageStatus(client_message_id, 'sent')
    Socket Saga->>Redux Store: dispatch(sendMessageSuccess)

    Note over Server,Receiver: Message Delivery
    Server->>Receiver: Forward message to receiver's socket
    Receiver->>Receiver: Save received message to local DB
    Receiver->>Receiver: Update UI with new message
    Receiver-->>Server: Send delivery acknowledgment
    Server-->>WebSocket: message_delivered event
    WebSocket-->>Socket Saga: Handle delivery confirmation
    Socket Saga->>Local DB: updateMessageStatus(client_message_id, 'delivered')
    Socket Saga->>WebSocket: socket.emit('delivery_confirmed', message_id)

    Note over MessageInput: UI Updates
    Redux Store-->>MessageInput: Update connection status
    MessageInput->>MessageInput: Clear input field & focus
    MessageInput->>User: Show message in chat UI

    Note over User,Receiver: Media Messages (Future Implementation)
    User->>MessageInput: Tap camera/attachment button
    MessageInput->>MessageInput: Open camera/gallery picker
    MessageInput->>MessageInput: Select image/video/file
    MessageInput->>MessageInput: Compress & prepare media
    MessageInput->>Socket Saga: sendMessageRequest(mediaData)
    Socket Saga->>Server: Upload media file via REST API
    Server->>Server: Store media file & generate unique ID
    Server-->>Socket Saga: Return media ID/URL
    Socket Saga->>Local DB: saveMessage(sender, receiver, mediaId, true, 'media', 'uploading')
    Local DB-->>Socket Saga: Return saved message with ID
    Socket Saga->>WebSocket: socket.emit('message', {mediaId, type: 'media'}, ackCallback)
    WebSocket->>Server: Forward media message with ID
    Server->>Server: Process message & link to stored media
    Server-->>WebSocket: Acknowledgment with message ID
    WebSocket-->>Socket Saga: ackCallback(response)
    Socket Saga->>Local DB: updateMessageStatus(client_message_id, 'sent')
    Server->>Receiver: Forward media message with ID
    Receiver->>Receiver: Save media message to local DB
    Receiver->>Server: Fetch media file using media ID
    Server-->>Receiver: Return media file
    Receiver->>Receiver: Display media content
    Receiver-->>Server: Send delivery acknowledgment
    Server-->>WebSocket: message_delivered event
    Socket Saga->>Local DB: updateMessageStatus(client_message_id, 'delivered')

    Note over User,Receiver: Optional Features
    Note over MessageInput: Typing Indicators
    MessageInput->>Socket Saga: sendMessageRequest(typing indicator)
    Socket Saga->>WebSocket: socket.emit('message', typingData)
    WebSocket->>Server: Forward typing indicator
    Server->>Receiver: Send typing indicator
    Receiver->>Receiver: Update typing UI

    Note over MessageInput: Emoji Reactions
    User->>MessageInput: Long press emoji
    MessageInput->>Socket Saga: sendMessageRequest(emoji reaction)
    Socket Saga->>WebSocket: socket.emit('message', emojiData)
    WebSocket->>Server: Forward emoji reaction
    Server->>Receiver: Send emoji reaction
    Receiver->>Receiver: Update emoji reaction UI 