import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { S3Client, GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

@Injectable()
export class S3ConfigService {
  private s3Client: S3Client;
  private bucketName: string;
  private useS3: boolean;

  constructor(private configService: ConfigService) {
    // S3 is now mandatory for media storage
    this.useS3 = true;
    this.bucketName = this.configService.get('AWS_S3_BUCKET') || '';

    const accessKeyId = this.configService.get('AWS_ACCESS_KEY_ID');
    const secretAccessKey = this.configService.get('AWS_SECRET_ACCESS_KEY');

    if (!accessKeyId || !secretAccessKey || !this.bucketName) {
      throw new Error('AWS credentials and bucket name are required for media storage');
    }

    this.s3Client = new S3Client({
      region: this.configService.get('AWS_REGION') || 'us-east-1',
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
    });
  }

  getS3Client(): S3Client {
    return this.s3Client;
  }

  getBucketName(): string {
    return this.bucketName;
  }

  isS3Enabled(): boolean {
    return this.useS3;
  }

  getS3Url(key: string): string {
    const region = this.configService.get('AWS_REGION') || 'us-east-1';
    return `https://${this.bucketName}.s3.${region}.amazonaws.com/${key}`;
  }

  /**
   * Generate a presigned URL for downloading a file from S3
   * @param key S3 object key
   * @param expiresIn Expiration time in seconds (default: 1 hour)
   * @returns Presigned URL for GET operation
   */
  async generatePresignedGetUrl(key: string, expiresIn: number = 3600): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: this.bucketName,
      Key: key,
    });

    return getSignedUrl(this.s3Client, command, { expiresIn });
  }

  /**
   * Generate a presigned URL for uploading a file to S3
   * @param key S3 object key
   * @param contentType MIME type of the file
   * @param expiresIn Expiration time in seconds (default: 1 hour)
   * @returns Presigned URL for PUT operation
   */
  async generatePresignedPutUrl(key: string, contentType: string, expiresIn: number = 3600): Promise<string> {
    const command = new PutObjectCommand({
      Bucket: this.bucketName,
      Key: key,
      ContentType: contentType,
    });

    return getSignedUrl(this.s3Client, command, { expiresIn });
  }
}
