import { call, put, takeLatest } from 'redux-saga/effects';
import { PayloadAction } from '@reduxjs/toolkit';
import { messageAPI } from '../../services/api';
import { chatDBService } from '../../database/service';
import { debugLog } from '../../utils/env';
import {
  syncAllMessagesRequest,
  syncAllMessagesSuccess,
  syncAllMessagesFailure,
  syncConversationMessagesRequest,
  syncConversationMessagesSuccess,
  syncConversationMessagesFailure,
  syncPendingMessagesRequest,
  syncPendingMessagesSuccess,
  syncPendingMessagesFailure,
  updateSyncProgress,
  MessageSyncData,
} from '../slices/messageSyncSlice';

/**
 * Sync a single message to WatermelonDB
 * Handles both new messages and status updates
 */
function* syncSingleMessage(serverMessage: MessageSyncData, currentUsername: string): Generator<any, void, any> {
  try {
    const {
      sender_username,
      receiver_username,
      message,
      type,
      status,
      client_message_id,
    } = serverMessage;

    // Determine if this is the current user's message
    const isMine = sender_username === currentUsername;

    // Check if message already exists in local database using client_message_id
    let existingMessage = null;

    if (client_message_id) {
      // Use client_message_id to find existing message (most reliable)
      existingMessage = yield call(chatDBService.getMessageById, client_message_id);
      debugLog(`Looking for message with client_message_id: ${client_message_id}, found: ${existingMessage ? 'yes' : 'no'}`);
    }

    // Skip delivered messages from other users that don't exist locally
    // These were already synced previously, so no need to sync again
    if (status === 'delivered' && !isMine && !existingMessage) {
      debugLog(`Skipping delivered message from other user (${sender_username}) - already synced previously`);
      return;
    }

    if (existingMessage) {
      // Message exists, check if we need to update status
      if (existingMessage.status !== status) {
        debugLog(`Updating message status from ${existingMessage.status} to ${status} for ${isMine ? 'own' : 'other'} message`);
        yield call(chatDBService.updateMessageStatus, existingMessage.id, status);
      }
    } else if (!existingMessage && !isMine) {
      // New message, create it
      debugLog(`Creating new ${isMine ? 'own' : 'other'} message with status ${status}: ${message.substring(0, 50)}...`);
      yield call(
        chatDBService.saveMessage,
        sender_username,
        receiver_username,
        message,
        isMine,
        type,
        status,
        currentUsername // selectedUser parameter
      );
    } else {
      debugLog(`Skipping delivered message from other user (${sender_username}) - already synced previously`);
    }
  } catch (error) {
    console.error('Failed to sync single message:', error);
    // Don't throw here to allow other messages to continue syncing
  }
}

/**
 * Worker saga for syncing all messages
 */
function* syncAllMessagesSaga(action: PayloadAction<{ username: string }>): Generator<any, void, any> {
  try {
    const { username } = action.payload;
    debugLog('Starting message sync for user:', username);

    // Fetch all messages from the server
    const serverMessages: MessageSyncData[] = yield call(messageAPI.getAllMessages);
    debugLog(`Fetched ${serverMessages.length} messages from server`);

    if (serverMessages.length === 0) {
      debugLog('No messages to sync');
      yield put(syncAllMessagesSuccess({ messageCount: 0 }));
      return;
    }

    // Update progress
    yield put(updateSyncProgress({ total: serverMessages.length, processed: 0 }));

    // Process each message
    for (let i = 0; i < serverMessages.length; i++) {
      const serverMessage = serverMessages[i];
      yield call(syncSingleMessage, serverMessage, username);

      // Update progress
      yield put(updateSyncProgress({ total: serverMessages.length, processed: i + 1 }));
    }

    debugLog('Message sync completed successfully');
    yield put(syncAllMessagesSuccess({ messageCount: serverMessages.length }));
  } catch (error: any) {
    console.error('Failed to sync messages:', error);
    yield put(syncAllMessagesFailure(error.message || 'Failed to sync messages'));
  }
}

/**
 * Worker saga for syncing conversation messages
 */
function* syncConversationMessagesSaga(action: PayloadAction<{ username: string; otherUsername: string }>): Generator<any, void, any> {
  try {
    const { username, otherUsername } = action.payload;
    debugLog(`Syncing conversation messages between ${username} and ${otherUsername}`);

    // Fetch conversation messages from the server
    const serverMessages: MessageSyncData[] = yield call(messageAPI.getConversationMessages, otherUsername);
    debugLog(`Fetched ${serverMessages.length} conversation messages from server`);

    if (serverMessages.length === 0) {
      debugLog('No conversation messages to sync');
      yield put(syncConversationMessagesSuccess({ messageCount: 0 }));
      return;
    }

    // Process each message
    for (const serverMessage of serverMessages) {
      yield call(syncSingleMessage, serverMessage, username);
    }

    debugLog('Conversation message sync completed successfully');
    yield put(syncConversationMessagesSuccess({ messageCount: serverMessages.length }));
  } catch (error: any) {
    console.error('Failed to sync conversation messages:', error);
    yield put(syncConversationMessagesFailure(error.message || 'Failed to sync conversation messages'));
  }
}

/**
 * Worker saga for syncing pending messages
 */
function* syncPendingMessagesSaga(action: PayloadAction<{ username: string }>): Generator<any, void, any> {
  try {
    const { username } = action.payload;
    debugLog('Syncing pending messages for user:', username);

    // Fetch pending messages from the server
    const pendingMessages: MessageSyncData[] = yield call(messageAPI.getPendingMessages);
    debugLog(`Fetched ${pendingMessages.length} pending messages from server`);

    if (pendingMessages.length === 0) {
      debugLog('No pending messages to sync');
      yield put(syncPendingMessagesSuccess({ messageCount: 0 }));
      return;
    }

    // Process each pending message
    for (const pendingMessage of pendingMessages) {
      yield call(syncSingleMessage, pendingMessage, username);
    }

    debugLog('Pending message sync completed successfully');
    yield put(syncPendingMessagesSuccess({ messageCount: pendingMessages.length }));
  } catch (error: any) {
    console.error('Failed to sync pending messages:', error);
    yield put(syncPendingMessagesFailure(error.message || 'Failed to sync pending messages'));
  }
}

/**
 * Root message sync saga
 */
export function* messageSyncSaga(): Generator<any, void, any> {
  yield takeLatest(syncAllMessagesRequest.type, syncAllMessagesSaga);
  yield takeLatest(syncConversationMessagesRequest.type, syncConversationMessagesSaga);
  yield takeLatest(syncPendingMessagesRequest.type, syncPendingMessagesSaga);
}
