/**
 * Development Configuration
 * 
 * This file contains development-specific configuration that can be easily
 * modified for different development environments.
 * 
 * Note: Most configuration is now handled by .env files, but this file
 * provides fallback values and helper functions.
 */

import { env } from '../utils/env';

// Fallback values (used if .env variables are not set)
export const FALLBACK_LOCAL_IP = '***********';
export const FALLBACK_BACKEND_PORT = '3002';

// Get current configuration from environment
export const getCurrentConfig = () => ({
  LOCAL_IP: env.LOCAL_IP || FALLBACK_LOCAL_IP,
  BACKEND_PORT: env.BACKEND_PORT || FALLBACK_BACKEND_PORT,
  API_URL: env.API_URL,
  WS_URL: env.WS_URL,
  ENV: env.ENV,
  DEBUG: env.DEBUG,
});

// Development URLs (with fallbacks)
export const DEV_API_URL = env.API_URL || `http://${env.LOCAL_IP || FALLBACK_LOCAL_IP}:${env.BACKEND_PORT || FALLBACK_BACKEND_PORT}`;
export const DEV_WS_URL = env.WS_URL || `ws://${env.LOCAL_IP || FALLBACK_LOCAL_IP}:${env.BACKEND_PORT || FALLBACK_BACKEND_PORT}`;

// Development configuration object
export const DEV_CONFIG = {
  API_URL: DEV_API_URL,
  WS_URL: DEV_WS_URL,
  ENV: env.ENV,
  DEBUG: env.DEBUG,
  LOG_LEVEL: env.LOG_LEVEL || 'debug',
  ENABLE_LOGGING: env.ENABLE_LOGGING,
};

// Helper function to get development config
export const getDevConfig = () => DEV_CONFIG;

// Helper function to update local IP (useful if your IP changes)
export const updateLocalIP = (newIP: string) => {
  const updatedConfig = {
    ...DEV_CONFIG,
    API_URL: `http://${newIP}:${env.BACKEND_PORT || FALLBACK_BACKEND_PORT}`,
    WS_URL: `ws://${newIP}:${env.BACKEND_PORT || FALLBACK_BACKEND_PORT}`,
  };
  return updatedConfig;
};

// Helper function to validate environment configuration
export const validateEnvConfig = () => {
  const config = getCurrentConfig();
  const issues = [];

  if (!config.API_URL) {
    issues.push('API_URL is not set in .env file');
  }
  if (!config.WS_URL) {
    issues.push('WS_URL is not set in .env file');
  }
  if (!config.LOCAL_IP) {
    issues.push('LOCAL_IP is not set in .env file');
  }

  return {
    isValid: issues.length === 0,
    issues,
    config,
  };
}; 