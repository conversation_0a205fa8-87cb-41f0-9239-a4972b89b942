sequenceDiagram
    participant User
    participant Frontend as "Frontend (Mobile/Web)"
    participant Backend as "Backend API (NestJS)"
    participant DB as "Database"
    participant Media as "Media Storage"
    participant FCM as "Push Notification Service (FCM)"
    participant Recipient

    User->>Frontend: Select media and type message
    Frontend->>Backend: Upload media file
    Backend->>Media: Store media file
    Media-->>Backend: Return media URL/ID
    Backend-->>Frontend: Return media URL/ID
    Frontend->>Backend: Send message with media URL/ID (REST/WebSocket)
    Backend->>DB: Save message with media reference
    par Real-time delivery (if recipient online)
        Backend->>Recipient: Deliver message via WebSocket
    and Push notification (if recipient offline/background)
        Backend->>FCM: Send push notification to recipient
        FCM-->>Recipient: Deliver notification
    end
    Note over Recipient: If received via push notification
    Recipient->>Backend: Fetch new message
    Backend->>Recipient: Return message data 