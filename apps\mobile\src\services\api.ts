import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { getApiUrl } from '../utils/env';

// Create axios instance with default configuration
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: getApiUrl(),
    timeout: 30000, // 30 seconds
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor to add auth token
  instance.interceptors.request.use(
    (config) => {
      // Get token from storage or Redux store
      const token = getAuthToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor for error handling
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      return response;
    },
    (error) => {
      // Handle common errors
      if (error.response?.status === 401) {
        // Token expired or invalid - trigger logout
        handleAuthError();
      }
      return Promise.reject(error);
    }
  );

  return instance;
};

// Get auth token from storage (you can modify this based on your storage method)
const getAuthToken = (): string | null => {
  // This should get token from your storage (AsyncStorage, Redux, etc.)
  // For now, we'll return null and let the saga handle it
  return null;
};

// Handle authentication errors
const handleAuthError = () => {
  // Dispatch logout action or clear token
  console.log('Authentication error - token may be expired');
};

// Create the API instance
export const api = createApiInstance();

// API response types
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  message?: string;
}

export interface MediaUploadResponse {
  id: string;
  original_filename: string;
  filename: string;
  mime_type: string;
  file_size: number;
  media_type: string;
  file_path: string;
  uploaded_by: string;
  width?: number;
  height?: number;
  duration?: number;
  thumbnail_path?: string;
  s3_key?: string;
  uploaded_at: string;
}

export interface MediaSignedUrlResponse {
  media: MediaUploadResponse;
  signedUrl: string;
  expiresAt: string;
}

// API service class
export class ApiService {
  private static instance: ApiService;
  private api: AxiosInstance;

  private constructor() {
    this.api = api;
  }

  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }

  // Set auth token (called from saga when user logs in)
  public setAuthToken(token: string): void {
    this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  // Clear auth token (called from saga when user logs out)
  public clearAuthToken(): void {
    delete this.api.defaults.headers.common['Authorization'];
  }

  // Media upload
  async uploadMedia(file: any, onProgress?: (progress: number) => void): Promise<MediaUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    const config: AxiosRequestConfig = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    };

    const response = await this.api.post<MediaUploadResponse>('/api/media/upload', formData, config);
    return response.data;
  }

  // Get media signed URL for download/view
  async getMediaSignedUrl(mediaId: string, expiresIn?: number): Promise<MediaSignedUrlResponse> {
    const params = expiresIn ? { expiresIn } : {};
    const response = await this.api.get<MediaSignedUrlResponse>(`/api/media/${mediaId}/signed-url`, { params });
    return response.data;
  }

  // Get media info
  async getMediaInfo(mediaId: string): Promise<MediaUploadResponse> {
    const response = await this.api.get<MediaUploadResponse>(`/api/media/${mediaId}/info`);
    return response.data;
  }

  // Delete media
  async deleteMedia(mediaId: string): Promise<{ message: string }> {
    const response = await this.api.delete<{ message: string }>(`/api/media/${mediaId}`);
    return response.data;
  }

  // Auth endpoints
  async login(credentials: { username: string; password: string }): Promise<{ access_token: string; refresh_token: string }> {
    const response = await this.api.post<{ access_token: string; refresh_token: string }>('/auth/login', credentials);
    return response.data;
  }

  async register(userData: { username: string; password: string; email?: string }): Promise<{ access_token: string; refresh_token: string }> {
    const response = await this.api.post<{ access_token: string; refresh_token: string }>('/auth/register', userData);
    return response.data;
  }

  async refreshToken(): Promise<{ access_token: string; refresh_token: string }> {
    const response = await this.api.post<{ access_token: string; refresh_token: string }>('/auth/refresh');
    return response.data;
  }

  // Message endpoints
  async getAllMessages(): Promise<any[]> {
    const response = await this.api.get<any[]>('/api/messages');
    return response.data;
  }

  async getConversationMessages(otherUsername: string): Promise<any[]> {
    const response = await this.api.get<any[]>(`/api/messages/conversation/${otherUsername}`);
    return response.data;
  }

  async getPendingMessages(): Promise<any[]> {
    const response = await this.api.get<any[]>('/api/messages/pending');
    return response.data;
  }
}

// Export singleton instance
export const apiService = ApiService.getInstance();

// Authentication service wrapper for consistent API
export const authService = {
  // Login with username and password
  login: async (username: string, password: string): Promise<{ access_token: string; refresh_token: string }> => {
    const response = await apiService.login({ username, password });
    return {
      access_token: response.access_token,
      refresh_token: response.refresh_token,
    };
  },

  // Register with username and password
  register: async (username: string, password: string): Promise<{ access_token: string; refresh_token: string }> => {
    const response = await apiService.register({ username, password });
    return {
      access_token: response.access_token,
      refresh_token: response.refresh_token,
    };
  },
};

// Message API endpoints (similar to frontend)
export const messageAPI = {
  // Get all messages for the authenticated user
  getAllMessages: async () => {
    return await apiService.getAllMessages();
  },

  // Get messages for a specific conversation
  getConversationMessages: async (otherUsername: string) => {
    return await apiService.getConversationMessages(otherUsername);
  },

  // Get pending messages for the authenticated user
  getPendingMessages: async () => {
    return await apiService.getPendingMessages();
  },
};
