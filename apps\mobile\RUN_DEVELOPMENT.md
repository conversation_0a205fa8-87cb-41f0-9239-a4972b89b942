# Running Mobile App in Development Mode

This guide will walk you through running the mobile app in development mode so it can connect to your local backend.

## Prerequisites

Before running the app, make sure you have:

1. **Node.js** (version 18 or higher)
2. **React Native CLI** installed globally
3. **Android Studio** (for Android development)
4. **Xcode** (for iOS development, macOS only)
5. **Your backend server** running on `localhost:3002`

## Step 1: Install Dependencies

First, navigate to the mobile app directory and install dependencies:

```bash
cd apps/mobile
yarn install
```

## Step 2: iOS Setup (macOS only)

If you're developing for iOS, you need to install CocoaPods dependencies:

```bash
# Install Ruby bundler (if not already installed)
gem install bundler

# Install CocoaPods dependencies
bundle install

# Install iOS dependencies
cd ios
bundle exec pod install
cd ..
```

## Step 3: Start Metro Bundler

Start the Metro bundler (React Native's JavaScript bundler):

```bash
# Start Metro with cache reset (recommended for first run)
yarn start:localhost

# OR start normally
yarn start
```

Keep this terminal window open - Metro needs to keep running.

## Step 4: Run the App

Open a **new terminal window** and run one of the following commands:

### For Android:

```bash
# Run on Android device/emulator
yarn android

# OR with localhost port forwarding (if using emulator)
yarn android:localhost
```

### For iOS (macOS only):

```bash
# Run on iOS simulator/device
yarn ios

# OR with localhost support
yarn ios:localhost
```

## Step 5: Verify Development Mode

When the app starts, you should see:

1. **Metro bundler** showing the app is connected
2. **Console logs** indicating development mode
3. **API requests** going to `http://***********:3002`

## Development Mode Features

When running in development mode (`__DEV__` is true), the app will:

- ✅ Connect to your local backend at `http://***********:3002`
- ✅ Enable debug logging
- ✅ Show development-specific UI indicators
- ✅ Enable hot reloading for code changes

## Troubleshooting

### Common Issues

#### 1. Metro Bundler Issues
```bash
# Clear Metro cache
yarn start:localhost

# OR manually clear cache
npx react-native start --reset-cache
```

#### 2. Android Build Issues
```bash
# Clean Android build
cd android
./gradlew clean
cd ..
yarn android
```

#### 3. iOS Build Issues
```bash
# Clean iOS build
cd ios
xcodebuild clean
cd ..
yarn ios
```

#### 4. Connection Issues
- Ensure your backend is running on `localhost:3002`
- Verify your IP address in `src/config/development.ts`
- Check that both devices are on the same network

### Network Configuration

If you're using an Android emulator, you might need port forwarding:

```bash
# Forward localhost ports to emulator
adb reverse tcp:3002 tcp:3002
```

## Development Workflow

1. **Start your backend**: `localhost:3002`
2. **Start Metro**: `yarn start:localhost`
3. **Run the app**: `yarn android` or `yarn ios`
4. **Make changes** to your code
5. **Save files** - changes will auto-reload
6. **Test API calls** - they should go to your local backend

## Useful Commands

```bash
# Start Metro bundler
yarn start

# Run on Android
yarn android

# Run on iOS
yarn ios

# Run tests
yarn test

# Lint code
yarn lint

# Start with Reactotron (for debugging)
yarn android:reactotron
```

## Environment Variables

The app automatically detects development mode and uses:
- **API URL**: `http://***********:3002`
- **WebSocket URL**: `ws://***********:3002`
- **Debug Mode**: Enabled
- **Logging**: Verbose

## Hot Reloading

When you make changes to your code:
- **JavaScript/TypeScript**: Auto-reloads
- **Native code**: Requires rebuild
- **Assets**: May require cache clear

## Debugging

### Reactotron (Optional)
```bash
# Install Reactotron globally
npm install -g reactotron

# Start Reactotron
reactotron

# Run app with Reactotron
yarn android:reactotron
```

### Chrome DevTools
1. Open Chrome
2. Navigate to `chrome://inspect`
3. Click "inspect" on your app

## Production vs Development

- **Development**: Uses local IP (`***********:3002`)
- **Production**: Uses cloudflare URLs
- **Automatic switching**: Based on `__DEV__` flag

## Next Steps

Once your app is running in development mode:

1. Test API connections to your backend
2. Verify WebSocket connections work
3. Test media uploads/downloads
4. Check authentication flow
5. Test push notifications (if configured)

## Support

If you encounter issues:
1. Check the console logs in Metro
2. Verify your backend is running
3. Check network connectivity
4. Review the `DEVELOPMENT_SETUP.md` file 