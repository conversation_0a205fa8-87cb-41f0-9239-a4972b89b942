import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';

export interface MessageSyncData {
  id: string;
  sender_username: string;
  receiver_username: string;
  message: string;
  type: 'text' | 'clear_chat' | 'typing' | 'delete_user' | 'system';
  timestamp: string;
  status: 'pending' | 'delivered';
  delivered_at: string | null;
  client_message_id?: string | null;
}

interface MessageSyncState {
  // Sync status
  isLoading: boolean;
  error: string | null;
  lastSyncTime: string | null;
  
  // Sync progress
  totalMessages: number;
  processedMessages: number;
  
  // Sync types status
  allMessagesSync: {
    isLoading: boolean;
    error: string | null;
    lastSync: string | null;
  };
  
  conversationSync: {
    isLoading: boolean;
    error: string | null;
    lastSync: string | null;
    currentConversation: string | null;
  };
  
  pendingMessagesSync: {
    isLoading: boolean;
    error: string | null;
    lastSync: string | null;
  };
}

const initialState: MessageSyncState = {
  isLoading: false,
  error: null,
  lastSyncTime: null,
  totalMessages: 0,
  processedMessages: 0,
  
  allMessagesSync: {
    isLoading: false,
    error: null,
    lastSync: null,
  },
  
  conversationSync: {
    isLoading: false,
    error: null,
    lastSync: null,
    currentConversation: null,
  },
  
  pendingMessagesSync: {
    isLoading: false,
    error: null,
    lastSync: null,
  },
};

const messageSyncSlice = createSlice({
  name: 'messageSync',
  initialState,
  reducers: {
    // All Messages Sync Actions
    syncAllMessagesRequest: (state, action: PayloadAction<{ username: string }>) => {
      state.allMessagesSync.isLoading = true;
      state.allMessagesSync.error = null;
      state.isLoading = true;
      state.error = null;
      state.totalMessages = 0;
      state.processedMessages = 0;
    },
    
    syncAllMessagesSuccess: (state, action: PayloadAction<{ messageCount: number }>) => {
      state.allMessagesSync.isLoading = false;
      state.allMessagesSync.lastSync = new Date().toISOString();
      state.isLoading = false;
      state.lastSyncTime = new Date().toISOString();
      state.totalMessages = action.payload.messageCount;
      state.processedMessages = action.payload.messageCount;
    },
    
    syncAllMessagesFailure: (state, action: PayloadAction<string>) => {
      state.allMessagesSync.isLoading = false;
      state.allMessagesSync.error = action.payload;
      state.isLoading = false;
      state.error = action.payload;
    },
    
    // Conversation Messages Sync Actions
    syncConversationMessagesRequest: (state, action: PayloadAction<{ username: string; otherUsername: string }>) => {
      state.conversationSync.isLoading = true;
      state.conversationSync.error = null;
      state.conversationSync.currentConversation = action.payload.otherUsername;
      state.isLoading = true;
      state.error = null;
    },
    
    syncConversationMessagesSuccess: (state, action: PayloadAction<{ messageCount: number }>) => {
      state.conversationSync.isLoading = false;
      state.conversationSync.lastSync = new Date().toISOString();
      state.conversationSync.currentConversation = null;
      state.isLoading = false;
    },
    
    syncConversationMessagesFailure: (state, action: PayloadAction<string>) => {
      state.conversationSync.isLoading = false;
      state.conversationSync.error = action.payload;
      state.conversationSync.currentConversation = null;
      state.isLoading = false;
      state.error = action.payload;
    },
    
    // Pending Messages Sync Actions
    syncPendingMessagesRequest: (state, action: PayloadAction<{ username: string }>) => {
      state.pendingMessagesSync.isLoading = true;
      state.pendingMessagesSync.error = null;
      state.isLoading = true;
      state.error = null;
    },
    
    syncPendingMessagesSuccess: (state, action: PayloadAction<{ messageCount: number }>) => {
      state.pendingMessagesSync.isLoading = false;
      state.pendingMessagesSync.lastSync = new Date().toISOString();
      state.isLoading = false;
    },
    
    syncPendingMessagesFailure: (state, action: PayloadAction<string>) => {
      state.pendingMessagesSync.isLoading = false;
      state.pendingMessagesSync.error = action.payload;
      state.isLoading = false;
      state.error = action.payload;
    },
    
    // Progress tracking
    updateSyncProgress: (state, action: PayloadAction<{ total: number; processed: number }>) => {
      state.totalMessages = action.payload.total;
      state.processedMessages = action.payload.processed;
    },
    
    // Clear errors
    clearSyncError: (state) => {
      state.error = null;
      state.allMessagesSync.error = null;
      state.conversationSync.error = null;
      state.pendingMessagesSync.error = null;
    },
    
    // Reset sync state
    resetSyncState: (state) => {
      return initialState;
    },
  },
});

// Export actions
export const {
  syncAllMessagesRequest,
  syncAllMessagesSuccess,
  syncAllMessagesFailure,
  syncConversationMessagesRequest,
  syncConversationMessagesSuccess,
  syncConversationMessagesFailure,
  syncPendingMessagesRequest,
  syncPendingMessagesSuccess,
  syncPendingMessagesFailure,
  updateSyncProgress,
  clearSyncError,
  resetSyncState,
} = messageSyncSlice.actions;

// Selectors
export const selectMessageSyncState = (state: RootState) => state.messageSync;
export const selectIsSyncing = (state: RootState) => state.messageSync.isLoading;
export const selectSyncError = (state: RootState) => state.messageSync.error;
export const selectLastSyncTime = (state: RootState) => state.messageSync.lastSyncTime;
export const selectSyncProgress = (state: RootState) => ({
  total: state.messageSync.totalMessages,
  processed: state.messageSync.processedMessages,
});

export const selectAllMessagesSyncState = (state: RootState) => state.messageSync.allMessagesSync;
export const selectConversationSyncState = (state: RootState) => state.messageSync.conversationSync;
export const selectPendingMessagesSyncState = (state: RootState) => state.messageSync.pendingMessagesSync;

// Export reducer
export default messageSyncSlice.reducer;
